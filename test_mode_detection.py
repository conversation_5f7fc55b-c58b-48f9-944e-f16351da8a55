#!/usr/bin/env python3
"""
Test script to verify mode detection works correctly.
"""

import sys
import os
from pathlib import Path

# Add the Windows_and_Linux directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'Windows_and_Linux'))

from config.settings import SettingsManager

def test_mode_detection():
    """Test mode detection in different scenarios."""
    print("Testing mode detection...")
    
    # Test 1: Normal dev mode (script execution)
    print("\n1. Testing normal dev mode (script execution):")
    manager = SettingsManager()
    print(f"   Detected mode: {manager.mode}")
    print(f"   Base dir: {manager.base_dir}")
    print(f"   Config dir: {manager.config_dir}")
    print(f"   Data file: {manager.data_file}")
    
    # Test 2: Simulate being in dist/dev/ directory
    print("\n2. Testing simulated dist/dev/ scenario:")
    
    # Temporarily modify sys.executable to simulate being in dist/dev/
    original_executable = getattr(sys, 'executable', '')
    original_frozen = getattr(sys, 'frozen', False)
    
    try:
        # Simulate frozen executable in dist/dev/
        sys.frozen = True
        sys.executable = str(Path.cwd() / "dist" / "dev" / "Writing Tools.exe")
        
        manager2 = SettingsManager()
        print(f"   Detected mode: {manager2.mode}")
        print(f"   Base dir: {manager2.base_dir}")
        print(f"   Config dir: {manager2.config_dir}")
        print(f"   Data file: {manager2.data_file}")
        
        # Check log file path
        log_path = manager2._get_log_file_path()
        print(f"   Log file: {log_path}")
        
    finally:
        # Restore original values
        if original_executable:
            sys.executable = original_executable
        else:
            delattr(sys, 'executable')
        sys.frozen = original_frozen
    
    print("\n✅ Mode detection test completed!")

if __name__ == "__main__":
    test_mode_detection()
